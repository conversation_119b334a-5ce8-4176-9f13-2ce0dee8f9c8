# 目标1 
## 目标描述
参与开发叽伴app

## 良好标准
1、负责的相关需求因为开发问题延期提测的次数小于等于。
2、负责的相关需求提测后因开发代码质量问题导致需求延期次数小于1。	

## 优秀标准
1、负责的相关需求因为开发问题延期提测的次数小于等于0。
2、负责的相关需求提测后因开发代码质量问题导致需求延期次数小于0。

## 自评详情：
1、负责app登录、首页ui、首页推送、角色生成中的角色生成流程进度显示、剧场等需求，都没有因为开发问题延期提测。 2、负责的相关需求没有因为代码质量问题导致延期，但是因为工期较紧导致，开发过程没有充分自测导致bug较多，不过都解决及时没有因此延期。

# 目标2
## 目标描述
新app基本框架搭建

## 良好标准
1、搭建app基本框架，合理组织给个模块功能区分。
2、实现并维护跨isolate调用框架，解决所有代码逻辑都在同一个线程运行的问题。
3、迁移原谜境剧本文件管理功能。
4、负责的相关组件，因设计或者功能缺陷导致开发问题次数=<1次，持续维护完善功能，为后续使用场景添加实现。
5、引入相关基础库，解决引入的库兼容问题和相关bug，兼容问题和bug解决率99%。

## 优秀标准
1、搭建app基本框架，合理组织给个模块功能区分。
2、实现并维护跨isolate调用框架，解决所有代码逻辑都在同一个线程运行的问题。
3、迁移原谜境剧本文件管理功能。
4、负责的相关组件，因设计或者功能缺陷导致开发问题次数=<1次，持续维护完善功能，为后续使用场景添加实现。
5、引入相关基础库，解决引入的库兼容问题和相关bug，兼容问题和bug解决率99%。

## 自评详情：
1、搭建了app基本框架，处理了路由框架还有melos导致的历史遗留问题，（路由兼容导致：用不了inspector每次使用要临时改代码，多package导致项目内存占用过大，卡顿）。 2、优化实现了跨线程调用框架，解决了所有代码逻辑都在同一个线程调用的问题。 3、相关功能完成迁移。 4、及时更新引入的组件的问题，解决了路由和lifecycle库的冲突问题，解决了引入的monorepo导致的app依赖问题。	

# 目标3
## 目标描述
日常需求开发和功能维护	

## 良好标准
1、负责的相关需求因为开发问题延期上线的次数小于等于1。
2、负责维护的相关功能上线后新增重大问题小于等于1。（影响正常功能使用）
3、负责的功能上线后bugly/firebase新增高频崩溃问题解决率达到80%。	

## 优秀标准
1、负责的相关需求都按预期时间完成开发，上线后无重大问题。
2、负责维护的相关功能上线后无新增重大问题。（影响正常功能使用）
3、负责的功能上线后bugly/firebase新增高频崩溃问题解决率达到100%。

# 自评
B+ 良好
1、上半季度在基本在开发维护谜境app，需求开发基本都没有因为开发问题延期提测。同时负责调研实现产品提出的谜境app梦境剧本加载流程优化（加快剧本加载，滑动切换剧本）需求，优化了加载速度达到产品要求。
2、下半季度负责搭建了新app的基本框架，解决了旧app部分框架和开发过程遇到的痛点，同时实现了一个跨isolate调用方案优化了使用方式，持续解决开发过程各个组件框架的适配问题。同时负责了app登录、首页ui、首页推送、角色生成中的角色生成流程进度显示、剧场等多个需求，且都没有因为开发问题导致需求延期。
